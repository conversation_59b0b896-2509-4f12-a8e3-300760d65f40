# BCTV Management System

A comprehensive web application for Beet Curly Top Virus (BCTV) hotspot prediction and eradication support in California agriculture.

## Overview

This Angular-based application provides field workers, researchers, and administrators with tools to:
- Record field observations of host plants and BLH populations
- Document BCTV symptoms and eradication efforts
- Generate AI-powered risk predictions
- Visualize data on interactive maps
- Track and analyze outbreak patterns

## Technology Stack

- **Frontend**: Angular 19 with SSR
- **Backend**: Supabase (PostgreSQL with PostGIS)
- **Mapping**: MapLibre GL JS
- **Storage**: Supabase Storage for photos
- **Authentication**: Supabase Auth

## Features

### ✅ Implemented
- User authentication and role-based access
- Interactive map dashboard with California focus
- Host plant observation forms (10 key BCTV host weeds)
- BLH (Beet Leafhopper) population tracking
- Photo upload with geolocation
- Basic rule-based risk prediction engine
- Mobile-optimized responsive design

### 🚧 In Development
- BCTV symptoms documentation
- Eradication effort tracking
- Advanced map visualizations
- Enhanced prediction algorithms

## Quick Start

### Prerequisites
- Node.js 18+
- Angular CLI 19+
- Supabase account

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd my-angular-app

# Install dependencies
npm install

# Set up environment variables (see Environment Setup below)
# Start development server
ng serve
```

### Environment Setup
Create a Supabase project and update the environment files:

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  supabase: {
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY'
  },
  maplibre: {
    style: 'https://demotiles.maplibre.org/style.json'
  }
};
```

## ⚠️ Current Known Issues

### Loading Screen Issue (In Progress)
**Status**: Under investigation and resolution
**Last Updated**: January 2025

**Symptoms**:
- Application builds successfully with only punycode deprecation warning
- App gets stuck on loading screen at http://localhost:4200/
- Supabase realtime connections are working (200 responses in ~6ms in dashboard)
- Browser shows default Angular welcome template instead of BCTV application

**Root Cause Analysis**:
1. **Template Issue**: `app.component.html` still contains default Angular template instead of BCTV app shell
2. **Routing Loop**: App redirects to `/dashboard` by default, but AuthGuard blocks unauthenticated users
3. **Missing Providers**: `app.config.ts` lacks necessary service providers for the application
4. **Auth Flow**: AuthService attempts immediate user profile loading, potentially causing async blocking

**Investigation Steps Completed**:
- ✅ Verified Supabase connectivity (realtime connections working)
- ✅ Confirmed build process works (no critical errors)
- ✅ Analyzed routing configuration and AuthGuard logic
- ✅ Identified template and service provider issues

**Next Steps for Resolution**:
1. Replace `app.component.html` with proper BCTV application shell
2. Update `app.config.ts` to include missing service providers
3. Fix routing logic to handle unauthenticated users properly
4. Add loading states during authentication checks
5. Test authentication flow for both authenticated/unauthenticated states

**Files Requiring Updates**:
- `src/app/app.component.html` - Replace default template
- `src/app/app.config.ts` - Add service providers
- `src/app/app.routes.ts` - Fix default route handling
- `src/app/core/services/auth.service.ts` - Improve async handling

**Debug Commands**:
```bash
# Start development server
ng serve

# Check browser console for errors at http://localhost:4200/
# Monitor Supabase dashboard for connection status
# Check network tab for failed requests
```

**Related Documentation**:
- `TROUBLESHOOTING.md` - Comprehensive technical analysis and resolution plan
- `LOADING_ISSUE_DEBUG.md` - Quick reference guide for AI assistants
- `DEVELOPMENT_PROGRESS.md` - Updated project status with critical issue tracking

## Application Structure

```
src/app/
├── core/
│   ├── models/          # Data models and types
│   ├── services/        # Core services (auth, data, etc.)
│   └── guards/          # Route guards
├── features/
│   ├── auth/           # Authentication components
│   ├── dashboard/      # Main dashboard with map
│   ├── data-entry/     # Data collection forms
│   └── predictions/    # Risk prediction views
├── shared/
│   └── components/     # Reusable components
└── environments/       # Environment configurations
```

## Key Components

### Data Models
- **Host Plants**: 10 key BCTV host weeds with density tracking
- **BLH Observations**: Population counts and behavior patterns
- **BCTV Symptoms**: Disease severity and symptom types
- **Eradication Efforts**: Control methods and effectiveness

### Prediction Engine
The application includes a rule-based prediction system that analyzes:
- Host plant density and distribution
- Beet leafhopper populations
- Weather conditions
- Seasonal factors
- Historical outbreak data

### Mobile Features
- GPS integration for accurate location data
- Photo capture and upload
- Offline-capable forms (planned)
- Touch-optimized interface

## User Roles

- **Field Worker**: Data collection and basic reporting
- **Researcher**: Advanced analytics and data export
- **Administrator**: User management and system configuration

## Development

### Running Tests
```bash
# Unit tests
ng test

# E2E tests
ng e2e
```

### Building for Production
```bash
ng build --configuration production
```

### Database Setup
See `DEVELOPMENT_PROGRESS.md` for detailed database schema and setup instructions.

## Contributing

1. Review the `DEVELOPMENT_PROGRESS.md` file for current status
2. Check the roadmap for planned features
3. Follow the established code structure and patterns
4. Ensure mobile responsiveness for all new features
5. Add appropriate tests for new functionality

## License

This project is developed for California agricultural research and management purposes.

## Support

For technical issues or feature requests, please refer to the development roadmap in `DEVELOPMENT_PROGRESS.md`.

---

**Note**: This application is specifically designed for California agricultural use and includes validation for California geographic bounds. The system focuses on the 10 key BCTV host weeds identified as critical for monitoring in California agriculture.
